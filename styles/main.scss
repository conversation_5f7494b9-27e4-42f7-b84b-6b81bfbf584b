// Variables
$primary-color: #2563eb; // blue-600
$primary-dark: #1d4ed8; // blue-700
$text-color: #1f2937; // gray-800
$light-text: #4b5563; // gray-600
$lighter-text: #9ca3af; // gray-400
$white: #ffffff;
$black: #000000;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Mixins
@mixin transition($property: all, $duration: 300ms) {
  transition: $property $duration ease;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

// Base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: $gray-100;
  color: $text-color;
  line-height: 1.5;

  &.dark {
    background-color: $gray-900;
    color: $white;
  }
}

// Components
@import 'components/header';
@import 'components/navigation';
@import 'components/buttons';
@import 'components/hero';
@import 'components/products';
@import 'components/about';
@import 'components/contact';
@import 'components/footer';
@import 'components/forms';
@import 'components/dark-mode';
