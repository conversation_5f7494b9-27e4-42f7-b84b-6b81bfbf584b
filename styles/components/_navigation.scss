.navigation {
  display: none;
  
  @media (min-width: 768px) {
    display: flex;
    gap: 2rem;
    align-items: center;
  }

  a {
    color: $light-text;
    text-decoration: none;
    @include transition;

    &:hover {
      color: $primary-color;
    }
  }
}

.mobile-menu-button {
  display: flex;
  align-items: center;
  
  @media (min-width: 768px) {
    display: none;
  }
}

.mobile-menu {
  display: none;
  background-color: $white;
  padding: 1rem 1.5rem;

  &.active {
    display: block;
  }

  .dark & {
    background-color: $gray-800;
  }

  a {
    display: block;
    padding: 0.5rem 0;
    color: $light-text;
    text-decoration: none;
    @include transition;

    &:hover {
      color: $primary-color;
    }
  }

  @media (min-width: 768px) {
    display: none !important;
  }
}
