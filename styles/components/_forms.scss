.form {
  &__group {
    margin-bottom: 1rem;
  }

  &__label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #d1d5db;
  }

  &__input,
  &__textarea {
    width: 100%;
    padding: 0.5rem 1rem;
    background-color: $gray-700;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    color: $white;
    @include transition;

    &:focus {
      outline: none;
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }

  &__textarea {
    resize: vertical;
    min-height: 100px;
  }

  &__button {
    background-color: $primary-color;
    color: $white;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    @include transition;

    &:hover {
      background-color: $primary-dark;
    }
  }
}
