.products {
  padding: 4rem 0;
  background-color: $white;

  @media (min-width: 768px) {
    padding: 6rem 0;
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    
    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__card {
    background-color: $gray-50;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: translateY(0);
    @include transition(transform);

    &:hover {
      transform: scale(1.05);
    }

    img {
      width: 100%;
      height: 14rem;
      object-fit: cover;
    }

    &-content {
      padding: 1.5rem;

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: $text-color;
        margin-bottom: 0.75rem;
      }

      p {
        color: $light-text;
      }
    }
  }
}
