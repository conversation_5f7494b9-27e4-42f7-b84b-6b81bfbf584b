.about {
  padding: 4rem 0;
  background-color: $white;

  @media (min-width: 768px) {
    padding: 6rem 0;
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
    align-items: center;

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__content {
    h2 {
      font-size: 2rem;
      font-weight: bold;
      color: $text-color;
      margin-bottom: 1rem;

      @media (min-width: 768px) {
        font-size: 2.25rem;
      }
    }

    p {
      color: $light-text;
      margin-bottom: 1rem;
      line-height: 1.75;
    }
  }

  &__features {
    list-style: none;
    margin-top: 1.5rem;

    li {
      display: flex;
      align-items: flex-start;
      margin-bottom: 0.5rem;
      color: $text-color;

      svg {
        width: 1.25rem;
        height: 1.25rem;
        color: $primary-color;
        margin-right: 0.5rem;
        margin-top: 0.25rem;
        flex-shrink: 0;
      }
    }
  }

  &__image {
    img {
      width: 100%;
      height: auto;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
  }
}
