.contact {
  padding: 4rem 0;
  background-color: $gray-800;
  color: #e5e7eb;

  @media (min-width: 768px) {
    padding: 6rem 0;
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__info {
    img {
      height: 120px;
      width: auto;
      margin-bottom: -35px;
    }

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: $white;
      margin-bottom: 1rem;
    }

    p {
      margin-bottom: 0.75rem;

      a {
        color: inherit;
        text-decoration: none;
        @include transition;

        &:hover {
          color: #60a5fa;
        }
      }
    }
  }
}
