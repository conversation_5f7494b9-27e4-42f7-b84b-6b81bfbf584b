.hero {
  position: relative;
  height: 70vh;
  min-height: 400px;
  @include flex-center;
  text-align: center;
  background-color: $gray-800;
  overflow: hidden;

  &__image {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.4;
  }

  &__content {
    position: relative;
    z-index: 10;
    padding: 2rem;

    h1 {
      font-size: 2.25rem;
      font-weight: bold;
      color: $white;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media (min-width: 768px) {
        font-size: 3.75rem;
      }

      span {
        color: #60a5fa;
      }
    }

    p {
      font-size: 1.125rem;
      color: #e5e7eb;
      margin-bottom: 2rem;
      max-width: 48rem;
      margin-left: auto;
      margin-right: auto;

      @media (min-width: 768px) {
        font-size: 1.25rem;
      }
    }
  }
}
