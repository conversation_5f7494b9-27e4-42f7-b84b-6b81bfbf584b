header {
  background-color: $white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  @include transition(background-color);

  .dark & {
    background-color: $gray-800;
  }

  nav {
    @include container;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}

.logo {
  text-decoration: none;
  font-size: 1.25rem;
  font-weight: bold;
  color: $primary-color;
  display: flex;
  align-items: center;

  img {
    height: 3rem;
    width: auto;
    margin-right: 0.5rem;
  }
}
