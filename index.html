<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechGear - 专业苹果配件供应商</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, #000428 0%, #004e92 100%);
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .contact-form input,
        .contact-form textarea {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-form input:focus,
        .contact-form textarea:focus {
            background-color: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>

<body class="font-sans text-gray-800 bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fab fa-apple text-gray-700 text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-gray-800">TechGear</span>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home"
                        class="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium transition-all">首页</a>
                    <a href="#about"
                        class="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium transition-all">公司介绍</a>
                    <a href="#products"
                        class="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium transition-all">产品中心</a>
                    <a href="#contact"
                        class="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium transition-all">联系我们</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="menu-btn" class="text-gray-700 hover:text-blue-600 focus:outline-none">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="md:hidden hidden bg-white shadow-lg">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#home"
                    class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-100 rounded-md font-medium">首页</a>
                <a href="#about"
                    class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-100 rounded-md font-medium">公司介绍</a>
                <a href="#products"
                    class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-100 rounded-md font-medium">产品中心</a>
                <a href="#contact"
                    class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-100 rounded-md font-medium">联系我们</a>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="home" class="hero-gradient text-white py-20 md:py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold leading-tight mb-6">专业苹果配件供应商</h1>
                    <p class="text-xl md:text-2xl mb-8 opacity-90">为您的苹果设备提供高品质配件解决方案</p>
                    <div class="flex space-x-4">
                        <a href="#products"
                            class="bg-white text-blue-700 hover:bg-gray-100 px-6 py-3 rounded-lg font-medium transition-all">查看产品</a>
                        <a href="#contact"
                            class="border border-white text-white hover:bg-white hover:text-blue-700 px-6 py-3 rounded-lg font-medium transition-all">联系我们</a>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <img src="https://images.unsplash.com/photo-1517336714731-489689fd1ca8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80"
                        alt="Apple Accessories" class="rounded-lg shadow-2xl max-w-full h-auto">
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">关于 TechGear</h2>
                <div class="w-20 h-1 bg-blue-600 mx-auto"></div>
            </div>
            <div class="md:flex items-center">
                <div class="md:w-1/2 mb-10 md:mb-0 md:pr-10">
                    <img src="https://images.unsplash.com/photo-1556740738-b6a63e27c4df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80"
                        alt="Our Office" class="rounded-lg shadow-lg w-full">
                </div>
                <div class="md:w-1/2">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">我们的故事</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        TechGear成立于2015年，是一家专注于苹果设备配件的专业供应商。我们致力于为全球客户提供高品质、创新设计的苹果配件产品，包括保护壳、充电器、耳机、数据线等各类配件。
                    </p>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        经过多年发展，我们已经建立了完善的供应链体系，与多家国际知名品牌建立了长期合作关系。我们的产品通过多项国际认证，品质有保障。
                    </p>
                    <div class="grid grid-cols-2 gap-4 mt-8">
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-full mr-4">
                                <i class="fas fa-check-circle text-blue-600"></i>
                            </div>
                            <span class="font-medium">品质保证</span>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-full mr-4">
                                <i class="fas fa-shipping-fast text-blue-600"></i>
                            </div>
                            <span class="font-medium">快速交付</span>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-full mr-4">
                                <i class="fas fa-headset text-blue-600"></i>
                            </div>
                            <span class="font-medium">专业支持</span>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-full mr-4">
                                <i class="fas fa-globe text-blue-600"></i>
                            </div>
                            <span class="font-medium">全球供应</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 数据统计 -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="p-6">
                    <div class="text-4xl font-bold text-blue-600 mb-2">30+</div>
                    <div class="text-gray-600">合作客户</div>
                </div>
                <div class="p-6">
                    <div class="text-4xl font-bold text-blue-600 mb-2">10+</div>
                    <div class="text-gray-600">产品种类</div>
                </div>
                <div class="p-6">
                    <div class="text-4xl font-bold text-blue-600 mb-2">10+</div>
                    <div class="text-gray-600">国家地区</div>
                </div>
                <div class="p-6">
                    <div class="text-4xl font-bold text-blue-600 mb-2">8年</div>
                    <div class="text-gray-600">行业经验</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 产品展示 -->
    <section id="products" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">我们的产品</h2>
                <div class="w-20 h-1 bg-blue-600 mx-auto"></div>
                <p class="text-gray-600 max-w-2xl mx-auto mt-6">我们提供全系列的苹果设备配件，满足不同客户的需求</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 产品1 -->
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden transition-all">
                    <div class="relative pb-2/3 h-64">
                        <img src="https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80"
                            alt="Screen Protectors" class="absolute h-full w-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">iPhone保护壳系列</h3>
                        <p class="text-gray-600 mb-4">采用优质材料，提供全方位保护，同时保持轻薄设计。</p>
                    </div>
                </div>

                <!-- 产品2 -->
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden transition-all">
                    <div class="relative pb-2/3 h-64">
                        <img src="https://images.unsplash.com/photo-1515940175183-6798529cb860?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80"
                            alt="Cables" class="absolute h-full w-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">快速充电器系列</h3>
                        <p class="text-gray-600 mb-4">支持PD快充，30分钟可充至50%，兼容多种苹果设备。</p>
                    </div>
                </div>

                <!-- 产品3 -->
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden transition-all">
                    <div class="relative pb-2/3 h-64">
                        <img src="https://images.unsplash.com/photo-1590658268037-6bf12165a8df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80"
                            alt="AirPods Cases" class="absolute h-full w-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">AirPods保护套</h3>
                        <p class="text-gray-600 mb-4">精致设计，防摔防刮，多种颜色可选，完美匹配您的风格。</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-16">
                <a href="#contact"
                    class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-all">获取完整产品目录</a>
            </div>
        </div>
    </section>

    <!-- 为什么选择我们 -->
    <section class="py-20 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">为什么选择 TechGear</h2>
                <div class="w-20 h-1 bg-blue-600 mx-auto"></div>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-lightbulb text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">创新设计</h3>
                    <p class="text-gray-600">专业设计团队持续创新，紧跟苹果产品更新节奏，推出匹配新品配件。</p>
                </div>

                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-award text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">品质保证</h3>
                    <p class="text-gray-600">所有产品均通过严格的质量检测，符合国际标准，部分产品获得苹果MFi认证。</p>
                </div>

                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-truck text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">快速交付</h3>
                    <p class="text-gray-600">拥有现代化仓储系统，订单处理高效，全球物流网络确保快速交付。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section id="contact" class="py-20 bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">联系我们</h2>
                <div class="w-20 h-1 bg-blue-600 mx-auto"></div>
                <p class="text-gray-300 max-w-2xl mx-auto mt-6">无论您有任何问题或合作意向，我们都期待您的联系</p>
            </div>

            <div class="md:flex">
                <div class="md:w-1/2 mb-10 md:mb-0 md:pr-10">
                    <h3 class="text-2xl font-bold mb-6">联系方式</h3>

                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="bg-blue-600 p-3 rounded-full mr-4">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h4 class="font-bold mb-1">公司地址</h4>
                                <p class="text-gray-300">中国深圳市南山区科技园路88号TechGear大厦15层</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-blue-600 p-3 rounded-full mr-4">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div>
                                <h4 class="font-bold mb-1">联系电话</h4>
                                <p class="text-gray-300">+86 755 1234 5678</p>
                                <p class="text-gray-300">+86 ************</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-blue-600 p-3 rounded-full mr-4">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h4 class="font-bold mb-1">电子邮箱</h4>
                                <p class="text-gray-300"><EMAIL></p>
                                <p class="text-gray-300"><EMAIL></p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-blue-600 p-3 rounded-full mr-4">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h4 class="font-bold mb-1">工作时间</h4>
                                <p class="text-gray-300">周一至周五: 9:00 - 18:00</p>
                                <p class="text-gray-300">周六: 10:00 - 16:00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地图 -->
                <div class="md:w-1/2">
                    <h3 class="text-2xl font-bold mb-6">　</h3>
                    <div class="h-96 bg-gray-800">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3691.86303661802!2d113.9453163154432!3d22.28478534915013!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3404014f1a1a1a1b%3A0x1a1a1a1a1a1a1a1a!2sTechGear%20Building!5e0!3m2!1sen!2sus!4v1620000000000!5m2!1sen!2sus"
                            width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-gray-300 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p>© 2023 TechGear. 保留所有权利。</p>
        </div>
    </footer>

    <script>
        // 移动端菜单切换
        const menuBtn = document.getElementById('menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        menuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                mobileMenu.classList.add('hidden');

                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 表单提交
        const contactForm = document.querySelector('.contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', function (e) {
                e.preventDefault();
                alert('感谢您的留言，我们会尽快与您联系！');
                this.reset();
            });
        }
    </script>
</body>

</html>